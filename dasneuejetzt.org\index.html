<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAS NEUE JETZT</title>
    <link rel="icon" type="image/png" href="assets/favicon.png">
    <link rel="stylesheet" href="styles.css">
</head>
<body>


    <!-- Banner Community -->
    <div class="banner banner-zusammenhalt" id="community">
        <h2>CO-KREATIVE COMMUNITY</h2>
    </div>

    <!-- Community Section -->
    <div class="section-box community">
        <p>Liebe Empathen, Pioniere und visionäre Menschen,</p>
        <p>die letzten Jahre haben wir unser Innen transformiert. Jetzt ist das Außen dran.</p>
        <p>Lasst uns verbinden: In Co-Kreation, mit unseren Visionen, Emotionen, Gaben, unserem Licht und unseren Schatten.</p>
        <p>Lasst es uns anders machen als bisher: <PERSON>cht mit Leistungsdruck, <PERSON><PERSON>mmenre<PERSON><PERSON><PERSON> und Effizienz, sondern mit Freude, <PERSON><PERSON><PERSON><PERSON>, Weichheit und Sinnhaftigkeit.</p>
        <p>Lasst uns einen Raum der Erlaubnis erschaffen: Erlaubnis, ein fühlendes Wesen zu sein, Macken und Fehler zu haben, uns einzubringen, zu erblühen und die Zukunft ins Jetzt zu bringen.</p>
        <p>Ich lade dich herzlich ein, dieser co-kreativen Community beizutreten und dich mit anderen Menschen zu verbinden, die zu deinen Werten und Visionen passen.</p>
        <a href="https://t.me/+44OLVhThCNdjYzU6" class="einheitsbutton" target="_blank">Tritt der Community bei</a>
    </div>

    <!-- Banner Events -->
    <div class="banner banner-fuehrung" id="events">
        <h2>CO-KREATIVE LIVE EVENTS</h2>
    </div>

    <!-- Events Section -->
    <div class="events-text">
        <p>Ich möchte gerne mit euch neue Formate für co-kreatives und liebevolles Miteinander ausprobieren. Wir lernen gemeinsam, was es wirklich braucht, um in einer Gruppe kreativ, im Flow und wirklich miteinander verbunden und gesehen zu sein.</p>
    </div>

    <!-- Events Grid - separate container for better width utilization -->
    <div class="events-grid-container">
        <div class="events-grid">
            <div class="event-card">
                <img src="assets\bilder\weekend.png" alt="Shadow & Light Weekend" class="event-image">
                <div class="event-content">
                    <h3 class="event-title">Shadow & Light Weekend</h3>
                    <p class="event-description">Bringe dich mit deinem Licht und deinen Schatten in eine Gruppe ein, so wie du bist</p>
                    <a href="weekend.html" class="einheitsbutton inactive">Coming soon</a>
                </div>
            </div>

            <div class="event-card">
                <img src="assets/bilder/week.png" alt="Co-Creation Week" class="event-image">
                <div class="event-content">
                    <h3 class="event-title">Co-Creation Week</h3>
                    <p class="event-description">Entwickle gemeinsam mit anderen in einer Woche eine Gründungsidee oder ein Online-Angebot, das zu deiner Vision passt</p>
                    <span class="einheitsbutton inactive">Coming sonn</span>
                </div>
            </div>

            <div class="event-card">
                <img src="assets\bilder\communityevent.png" alt="Community Event" class="event-image">
                <div class="event-content">
                    <h3 class="event-title">Community Event</h3>
                    <p class="event-description">Wenn alles möglich ist, wie sieht dann dein liebstes co-kreatives Event aus? Deine Ideen und Vorstellungen bilden die Basis für zukünftige Events!</p>
                    <span class="einheitsbutton">Formular ausfüllen</span>
                </div>
            </div>
        </div>
    </div>

    
    <!-- Banner Zwischenmenschlichkeit -->
    <div class="banner banner-zwischenmenschlichkeit" id="zwischenmenschlichkeit">
        <h2>NEUE ZWISCHENMENSCHLICHKEIT</h2>
    </div>

    <!-- Zwischenmenschlichkeit Section -->
    <div class="section-box">
        <p>Eine neue Grundhaltung als Basis unserer Zwischenmenschlichkeit:</p>
        <p><strong>Wohlwollende Absicht:</strong> Lass uns zusammenkommen in der Absicht, wirklich das Beste füreinander zu wollen.</p>
        <p><strong>Positives Interesse:</strong> Lass uns mit offener Neugier aufeinander zugehen und uns wirklich für die Perspektiven und den Lebensweg der anderen interessieren.</p>
        <p><strong>Allumfassende Annahme:</strong> Lass uns einen Raum erschaffen, in dem jede Person mit all ihren Facetten da sein darf.</p>
        <p><strong>Sichere Bindung:</strong> Lass uns auch Schwächen und Fehler annehmen, sodass Liebe und Bindung bedingungslos und sicher werden.</p>
        <p><strong>Positives Menschenbild:</strong> Lass uns die Absichten und Bedürfnisse von Menschen so tief erforschen, dass wir merken, dass sie im Kern gut sind, selbst wenn es auf den ersten Blick nicht so wirkt.</p>
        <p><strong>Kompatibilität:</strong> Lass uns prüfen, ob unsere Werte, Visionen, Streitkultur und Lebensausrichtung zueinander passen und diese Kompatibilität als Fundament für unsere Beziehung wählen.</p>
        <p><strong>Geschützter Raum zum Explorieren:</strong> Lass uns einen Raum kreieren, in dem so viel bedingungslose Annahme existiert, dass wir gemeinsam unsere Gefühlswelt, Bedürfnisse und Absichten erforschen können.</p>
        <p><strong>Wirklich verstehen wollen:</strong> Lass uns entscheiden, uns selbst und die anderen wirklich in der Tiefe verstehen, fühlen, hören und sehen zu wollen.</p>
        <p><strong>Projektionen durchschauen:</strong> Lass uns Triggerdynamiken gemeinsam durchstehen, unsere Projektionen durchschauen und unsere Anteile zu uns zurücknehmen.</p>
        <p><strong>Dialog statt Diskussion:</strong> Lass uns in einer Diskussion entscheiden, auf die gleiche Seite zu kommen und in einen Dialog zu treten, wo wir die Perspektive der anderen Person wirklich nachvollziehen.</p>
        <p><strong>An Herausforderungen wachsen:</strong> Lass uns zwischenmenschliche Schwierigkeiten als Anlass nehmen, gemeinsam zu heilen, zu wachsen und unsere Beziehung immer stärker und tiefer werden zu lassen.</p>
        <p><strong>Weichheit:</strong> Lass uns aufhören, uns zusammenzureißen und wieder weich mit uns selbst werden.</p>
        <p><strong>Intuition:</strong> Lass uns darauf vertrauen, dass jede Person im Inneren genau weiß, wo lang ihr Weg sie führt. Lass uns dieser inneren Führung wieder vertrauen.</p>
        <p><strong>Einzigartigkeit ehren:</strong> Lass uns erkennen, dass jede Person ihre ganz eigenen Gaben mit in diese Welt bringt, die alle gemeinsam ein wunderschönes Puzzle ergeben. Lass uns diese Gaben gemeinsam entdecken und erforschen.</p>
        <p><strong>Das eigene Tempo ehren:</strong> Lass uns erkennen, dass jeder Mensch genauso wie jede Pflanze in ihrem eigenen Tempo wächst und Menschen in ihrem Samen-Status genauso wertvoll sind wie Menschen in ihrem Baum-Status.</p>
        <p><strong>Gegenseitig nähren:</strong> Lass uns dafür sorgen, dass jede Person (so wie jede Pflanze) im wirklich passenden Nährboden sitzt und bekommt, was sie braucht, um zu erblühen.</p>
        <p><strong>Gegenseitige Unterstützung auf unserem Weg:</strong> Lass uns Hürden gemeinsam überwinden, uns gegenseitig ermutigen und supporten.</p>
        <p><strong>Co-Kreation:</strong> Lass uns in Co-Kreation gemeinsam Lösungen für die großen Probleme unserer Welt entwickeln statt gegeneinander zu konkurrieren.</p>
        <p><strong>Integration:</strong> Lass uns umfassende und ganzheitliche Lösungen entwickeln, indem wir unterschiedliche Perspektiven umarmen und integrieren statt sie gegeneinander auszuspielen.</p>
        <p><strong>Gute Lösungen finden:</strong> Lass uns "All-Win"-Lösungen entwickeln, bei denen niemand nachgeben muss, sondern die für alle Beteiligten gut sind.</p>
        <p><strong>Lass uns die Zukunft ins Jetzt holen:</strong> Lass uns die Zukunft schon im Jetzt gemeinsam miteinander leben!</p>
        <a href="https://t.me/+44OLVhThCNdjYzU6" class="einheitsbutton" target="_blank">Tritt der Community bei</a>
    </div>

    <!-- Banner Jana Sophie Breitmar -->
    <div class="banner banner-blumenfeld" id="jana-sophie">
        <h2>JANA SOPHIE BREITMAR</h2>
    </div>

    <!-- Jana Sophie Breitmar Section -->
    <div class="section-box">
        <img src="assets/bilder/profilbild.jpg" alt="Jana Sophie Breitmar" class="profile-image" id="profileImage">

        <h3>Meine Vision</h3>
        <p>Seit einigen Jahren wächst in mir ein Bild heran, wie eine liebevolle Zukunft voller Zwischenmenschlichkeit aussehen könnte. Eine Zukunft, in der wir wohlwollend miteinander umgehen, uns unterstützen, nähren und erblühen. Eine Zukunft, in der wir alle zusammenhalten und die großen Herausforderungen unserer Zeit gemeinsam angehen. Jetzt möchte ich eine Transformation anstoßen und diese Zukunft ins Jetzt holen. Ein Upgrade: Eine neue Welt mit mehr Zwischenmenschlichkeit, mehr integrierter Emotionalität, mehr Zusammenhalt. Echter, authentischer, verbundener, ganzer, schöner.</p>

        <h3>Mein Weg</h3>
        <p>Mein Weg verlief von einem Fokus auf Perfektionismus und Effizienz hin zur Integration von Emotionen und Intuition. Ich habe den Master-Studiengang "Innovations- & Change-Management" studiert und dabei gemerkt: das Thema Veränderung liegt mir, aber unsere Arbeitswelt ist von einer Effizienz-Mentalität geprägt, die uns langfristig kaputt macht. Im Laufe meines eigenen Entwicklungsprozesses durfte ich lernen, dass mich das am Ende nicht dahin führt, wo ich wirklich hinmöchte: in eine liebevolle Welt. In diese Welt führt uns die allumfassende Annahme unserer inneren Anteile, unserer Emotionen, unserer Ganzheit. Deswegen habe ich mich im weiteren Verlauf meines Weges viel mit der Arbeit mit meinen Emotionen, meinen alten Prägungen, Kränkungen, Traumata, mit meiner Intuition, meinen wahren Werten und meiner Vision beschäftigt. In diesem Bereich habe ich keine offizielle Ausbildung gemacht. Hier spreche ich aus persönlichen Erfahrungen, lebensverändernden Erkenntnissen, meiner höchsten Essenz und meinen intuitiv-fühlbaren Wahrheiten.</p>

        <h3>Meine Stärken</h3>
        <div class="strengths-grid">
            <span class="strength-tag">Verständnis</span>
            <span class="strength-tag">Raum für Emotionen</span>
            <span class="strength-tag">Menschen wirklich sehen</span>
            <span class="strength-tag">Tiefe herstellen</span>
            <span class="strength-tag">Bis zum Kern eines Themas graben</span>
            <span class="strength-tag">Dunkle Schatten liebevoll halten</span>
            <span class="strength-tag">Muster erkennen</span>
            <span class="strength-tag">Ideen generieren</span>
        </div>

        <h3>Verbinde dich mit mir</h3>
        <div class="social-links">
            <a href="https://instagram.com/@janasophiebreitmar" target="_blank" class="einheitsbutton instagram">Instagram</a>
            <a href="https://www.youtube.com/@janasophiebreitmar" target="_blank" class="einheitsbutton youtube">YouTube</a>
            <a href="https://open.spotify.com/intl-de/artist/3UtRmfHSN1nHEx3a0jWgwi?si=Qrqnbh4aReOK4flnzzpM6A" target="_blank" class="einheitsbutton spotify">Spotify</a>
            <a href="https://t.me/+44OLVhThCNdjYzU6" target="_blank" class="einheitsbutton telegram">Telegram</a>
        </div>
    </div>

    <!-- Banner CTA -->
    <div class="banner banner-zukunftinsjetzt" id="zukunftinsjetzt">
        <div class="banner-content">
            <h2>Lass uns die Zukunft ins Jetzt holen!</h2>
            <a href="https://t.me/+44OLVhThCNdjYzU6" class="einheitsbutton transparent" target="_blank">Tritt der Community bei</a>
        </div>
    </div>
    


    <script src="header-and-footer.js"></script>
    <script>
        // Profile Image Modal - for Jana Sophie section
        document.addEventListener('DOMContentLoaded', function() {
            const profileImage = document.querySelector('.profile-image');
            if (!profileImage) return;

            // Container für das Modal-Bild erstellen
            const imageModalContainer = document.createElement('div');
            imageModalContainer.className = 'profile-image-modal';
            imageModalContainer.style.display = 'none';
            document.documentElement.appendChild(imageModalContainer);

            // CSS für Modal-Bild hinzufügen
            const style = document.createElement('style');
            style.textContent = `
                .profile-image-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    width: 100vw;
                    height: 100vh;
                    background-color: rgba(0, 0, 0, 0.8);
                    z-index: 2000;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0;
                    padding: 0;
                }

                .modal-image-container {
                    position: relative;
                    z-index: 2001;
                }

                .profile-image-modal .modal-image {
                    width: 500px;
                    height: 500px;
                    max-width: 80vw;
                    max-height: 80vh;
                    border-radius: 50%;
                    object-fit: cover;
                    border: 6px solid #fdc950;
                    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
                    cursor: default;
                }

                @media (max-width: 768px) {
                    .profile-image-modal .modal-image {
                        width: 350px;
                        height: 350px;
                        max-width: 85vw;
                        max-height: 85vh;
                    }
                }

                @media (max-width: 480px) {
                    .profile-image-modal .modal-image {
                        width: 300px;
                        height: 300px;
                        max-width: 90vw;
                        max-height: 90vh;
                    }
                }
            `;
            document.head.appendChild(style);

            // Klick-Ereignis für das Profilbild hinzufügen
            profileImage.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Modal-Container mit Bild füllen und anzeigen
                imageModalContainer.innerHTML = `
                    <div class="modal-image-container">
                        <img src="${profileImage.src}" alt="${profileImage.alt}" class="modal-image">
                    </div>`;

                // Store original body styles
                const originalOverflow = document.body.style.overflow;
                const originalPaddingRight = document.body.style.paddingRight;

                // Prevent page shift by adding padding to compensate for scrollbar
                const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
                if (scrollbarWidth > 0) {
                    document.body.style.paddingRight = scrollbarWidth + 'px';
                }
                document.body.style.overflow = 'hidden';

                imageModalContainer.style.display = 'flex';

                // Event-Handler für das Schließen hinzufügen
                function imageModalClickHandler(e) {
                    // Schließen bei Klick auf Hintergrund
                    if (e.target === imageModalContainer) {
                        closeImageModal();
                    }
                }

                imageModalContainer.addEventListener('click', imageModalClickHandler);

                // Funktion zum Schließen des Bild-Modals
                function closeImageModal() {
                    imageModalContainer.style.display = 'none';
                    // Restore original styles
                    document.body.style.overflow = originalOverflow;
                    document.body.style.paddingRight = originalPaddingRight;
                    imageModalContainer.removeEventListener('click', imageModalClickHandler);
                }

                // Close modal on escape key
                function escapeHandler(event) {
                    if (event.key === 'Escape') {
                        closeImageModal();
                        document.removeEventListener('keydown', escapeHandler);
                    }
                }
                document.addEventListener('keydown', escapeHandler);
            });
        });
    </script>
</body>
</html>